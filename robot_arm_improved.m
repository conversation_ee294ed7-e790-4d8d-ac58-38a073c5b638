%% 4自由度R-P-P-R机器人 - 改进版：紫色机械臂末端斜直线运动+逆解控制
clear; clc; close all;

%% 机器人参数
robot.L_blue = 1.0;         % 蓝色臂长度
robot.W_blue = 0.06;        % 蓝色臂宽度
robot.L_yellow = 0.15;      % 黄色滑块长度
robot.W_yellow = 0.14;      % 黄色滑块宽度
robot.H_yellow = 0.05;      % 黄色滑块高度
robot.L_green = 0.6;        % 绿色臂长度
robot.W_green = 0.04;       % 绿色臂宽度
robot.L_purple = 0.1;       % 紫色执行器长度
robot.W_purple = 0.07;      % 紫色执行器宽度
robot.H_purple = 0.07;      % 紫色执行器高度

% D-H参数表 - 标准D-H参数 [theta, d, a, alpha]
robot.dh_params = [
    0,       0,                                     robot.L_blue,   0;        % 关节1: 蓝色臂绕X轴旋转
    0,       robot.W_blue/2 + robot.H_yellow/2,    0,              pi/2;     % 关节2: 黄色滑块沿X轴平移
    0,       0,                                     0,              -pi/2;    % 关节3: 绿色臂沿Y轴平移
    0,       robot.H_yellow/2 + robot.W_green/2,   robot.L_green + robot.L_purple,  pi/2;     % 关节4: 紫色机械臂绕X轴旋转，到达末端
];

% 关节类型定义 (1=旋转, 0=平移)
robot.joint_types = [1, 0, 0, 1];  % R-P-P-R

% 关节限制
green_slide_limit = (robot.W_yellow - robot.W_green) / 2;
robot.q_limits = [
    -deg2rad(60),  deg2rad(60);     % q1: 蓝色臂绕X轴旋转 ±60°
    0,             0.46;            % q2: 黄色滑块沿X轴平移 0-0.46m
    -green_slide_limit, green_slide_limit;  % q3: 绿色臂Y轴平移
    -pi,           pi               % q4: 紫色执行器绕X轴旋转 ±180°
];

%% 斜直线轨迹参数
trajectory.start_point = [0.8, -0.3, -0.1];    % 起始点 (X, Y, Z)
trajectory.end_point = [1.2, 0.3, 0.1];        % 终止点 (X, Y, Z)
trajectory.duration = 8.0;                      % 轨迹持续时间 (秒)
trajectory.direction = trajectory.end_point - trajectory.start_point;  % 轨迹方向向量
trajectory.length = norm(trajectory.direction); % 轨迹长度

%% 显示机器人信息
fprintf('=== 4自由度R-P-P-R机器人 - 改进版 ===\n');
fprintf('特点：紫色机械臂末端斜直线运动 + 逆解控制保持平行于X轴\n\n');
fprintf('D-H参数表:\n');
fprintf('关节 | 类型 |  theta  |    d    |    a    |  alpha  | 说明\n');
fprintf('-----|------|---------|---------|---------|---------|------------------\n');
for i = 1:size(robot.dh_params, 1)
    joint_type_str = {'P', 'R'};
    desc_str = {'蓝色臂(R)', '黄色滑块(P)', '绿色臂(P)', '紫色机械臂(R)'};
    
    fprintf(' %d   |  %s   | %7.3f | %7.3f | %7.3f | %7.3f | %s\n', ...
            i, joint_type_str{robot.joint_types(i)+1}, ...
            robot.dh_params(i,1), robot.dh_params(i,2), robot.dh_params(i,3), robot.dh_params(i,4), ...
            desc_str{i});
end

fprintf('\n轨迹信息:\n');
fprintf('起始点: [%.2f, %.2f, %.2f]\n', trajectory.start_point);
fprintf('终止点: [%.2f, %.2f, %.2f]\n', trajectory.end_point);
fprintf('轨迹长度: %.3f m\n', trajectory.length);
fprintf('持续时间: %.1f s\n\n', trajectory.duration);

%% 图形设置
figure('Name', 'R-P-P-R Robot - Improved with Diagonal Motion', 'NumberTitle', 'off', ...
       'Color', 'w', 'Position', [100, 100, 1400, 900]);
hold on; axis equal; grid on;
view(135, 25);
xlabel('X (m)'); ylabel('Y (m)'); zlabel('Z (m)');
title('4-DOF R-P-P-R Robot - Diagonal Motion with Inverse Kinematics Control');
xlim([-0.2, 1.4]); ylim([-0.8, 0.8]); zlim([-0.8, 0.3]);
camlight; lighting gouraud;

%% 视频设置
video_filename = 'rppr_robot_diagonal_motion.mp4';
video_writer = VideoWriter(video_filename, 'MPEG-4');
video_writer.FrameRate = 25;  % 25 fps
video_writer.Quality = 95;
open(video_writer);

%% 创建图形对象
unit_cube_vertices = [-0.5 -0.5 -0.5; 0.5 -0.5 -0.5; 0.5 0.5 -0.5; -0.5 0.5 -0.5;
                      -0.5 -0.5 0.5; 0.5 -0.5 0.5; 0.5 0.5 0.5; -0.5 0.5 0.5]';
unit_cube_faces = [1 2 6 5; 2 3 7 6; 3 4 8 7; 4 1 5 8; 1 2 3 4; 5 6 7 8];
initial_vertices = zeros(8, 3);

h_blue_arm = patch('Vertices', initial_vertices, 'Faces', unit_cube_faces, 'FaceColor', 'b', 'EdgeColor', 'k');
h_yellow_slider = patch('Vertices', initial_vertices, 'Faces', unit_cube_faces, 'FaceColor', 'y', 'EdgeColor', 'k');
h_green_arm = patch('Vertices', initial_vertices, 'Faces', unit_cube_faces, 'FaceColor', 'g', 'EdgeColor', 'k');
h_purple_effector = patch('Vertices', initial_vertices, 'Faces', unit_cube_faces, 'FaceColor', [0.6, 0.2, 0.8], 'EdgeColor', 'k');

% 轨迹线显示
h_trajectory = plot3([], [], [], 'r--', 'LineWidth', 2, 'DisplayName', '目标轨迹');
h_actual_path = plot3([], [], [], 'b-', 'LineWidth', 1.5, 'DisplayName', '实际路径');

% 绘制目标轨迹线
plot3([trajectory.start_point(1), trajectory.end_point(1)], ...
      [trajectory.start_point(2), trajectory.end_point(2)], ...
      [trajectory.start_point(3), trajectory.end_point(3)], ...
      'r--', 'LineWidth', 3, 'DisplayName', '目标斜直线轨迹');

% 参数显示文本框
h_text = annotation('textbox', [0.75, 0.7, 0.23, 0.25], 'String', 'Ready', 'FontSize', 10, ...
                   'VerticalAlignment', 'top', 'EdgeColor', 'k', 'BackgroundColor', 'w', 'FitBoxToText', 'on');

legend('Location', 'northeast');

%% 动画仿真
fprintf('开始改进版动画仿真和视频录制...\n');

dt = 0.04;  % 时间步长
frame_count = 0;
robot_offset_z = -0.3;  % 机器人整体下移偏移量

% 存储实际路径
actual_path_x = [];
actual_path_y = [];
actual_path_z = [];

% 存储逆解失败次数
ik_failures = 0;
total_attempts = 0;

for t = 0:dt:trajectory.duration
    total_attempts = total_attempts + 1;
    
    % 计算当前目标位置（斜直线插值）
    progress = t / trajectory.duration;  % 进度 [0, 1]
    target_pos = trajectory.start_point + progress * trajectory.direction;
    target_pos(3) = target_pos(3) + robot_offset_z;  % 应用Z轴偏移
    
    % 使用逆运动学求解关节角度
    try
        q_solution = inverse_kinematics_with_orientation_constraint(target_pos, robot);
        
        % 验证解的有效性
        [~, T_end] = forward_kinematics_dh(q_solution, robot);
        actual_pos = T_end(1:3, 4);
        actual_pos(3) = actual_pos(3) + robot_offset_z;
        
        position_error = norm(actual_pos - target_pos);
        
        if position_error > 0.05  % 如果误差太大，使用备用方法
            q_solution = fallback_inverse_kinematics(target_pos, robot);
            ik_failures = ik_failures + 1;
        end
        
    catch
        % 如果逆解失败，使用备用方法
        q_solution = fallback_inverse_kinematics(target_pos, robot);
        ik_failures = ik_failures + 1;
    end
    
    % 应用关节限制
    q_solution = apply_joint_limits(q_solution, robot);
    
    % 计算实际末端位置
    [~, T_end] = forward_kinematics_dh(q_solution, robot);
    actual_end_pos = T_end(1:3, 4);
    actual_end_pos(3) = actual_end_pos(3) + robot_offset_z;
    
    % 存储实际路径
    actual_path_x = [actual_path_x, actual_end_pos(1)];
    actual_path_y = [actual_path_y, actual_end_pos(2)];
    actual_path_z = [actual_path_z, actual_end_pos(3)];
    
    % 绘制机器人
    draw_robot_dh_with_offset(q_solution, robot, h_blue_arm, h_yellow_slider, h_green_arm, h_purple_effector, unit_cube_vertices, robot_offset_z);
    
    % 更新实际路径显示
    set(h_actual_path, 'XData', actual_path_x, 'YData', actual_path_y, 'ZData', actual_path_z);
    
    % 计算紫色机械臂的方向角度
    purple_orientation = rad2deg(q_solution(4));
    
    % 更新参数显示
    param_str = sprintf(['改进版机器人控制:\n' ...
                        '目标位置:\n  X: %.3f  Y: %.3f  Z: %.3f\n' ...
                        '实际位置:\n  X: %.3f  Y: %.3f  Z: %.3f\n' ...
                        '位置误差: %.4f m\n\n' ...
                        '关节角度:\n  q1: %+.1f°  q2: %.3fm\n  q3: %+.3fm  q4: %.1f°\n\n' ...
                        '紫色臂方向: %.1f°\n' ...
                        '轨迹进度: %.1f%%\n' ...
                        '逆解成功率: %.1f%%\n' ...
                        '帧数: %d'], ...
                       target_pos(1), target_pos(2), target_pos(3), ...
                       actual_end_pos(1), actual_end_pos(2), actual_end_pos(3), ...
                       norm(actual_end_pos - target_pos), ...
                       rad2deg(q_solution(1)), q_solution(2), q_solution(3), purple_orientation, ...
                       purple_orientation, progress*100, ...
                       (1 - ik_failures/total_attempts)*100, frame_count);
    set(h_text, 'String', param_str);
    
    drawnow;
    
    % 录制视频帧
    frame = getframe(gcf);
    writeVideo(video_writer, frame);
    frame_count = frame_count + 1;
    
    pause(dt);
end

% 关闭视频文件
close(video_writer);

fprintf('改进版动画完成！\n');
fprintf('视频已保存为: %s\n', video_filename);
fprintf('总帧数: %d\n', frame_count);
fprintf('逆解成功率: %.1f%%\n', (1 - ik_failures/total_attempts)*100);
fprintf('轨迹跟踪完成，紫色机械臂保持平行于X轴运动。\n');

%% ========== 核心函数定义 ==========

%% 带方向约束的逆运动学求解
function q_solution = inverse_kinematics_with_orientation_constraint(target_pos, robot)
    % 逆运动学求解，确保紫色机械臂保持平行于X轴

    % 初始猜测
    q0 = zeros(4, 1);
    for i = 1:4
        q0(i) = (robot.q_limits(i,1) + robot.q_limits(i,2)) / 2;
    end

    % 优化选项
    options = optimoptions('fmincon', 'Display', 'off', 'Algorithm', 'sqp', ...
                          'MaxIterations', 500, 'TolFun', 1e-6, 'TolX', 1e-6, ...
                          'MaxFunctionEvaluations', 2000);

    % 目标函数：位置误差 + 方向约束
    objective = @(q) calc_position_and_orientation_error(q, target_pos, robot);

    % 求解
    [q_solution, fval] = fmincon(objective, q0, [], [], [], [], ...
                                robot.q_limits(:,1), robot.q_limits(:,2), [], options);

    % 如果误差太大，抛出异常
    if fval > 0.1
        error('逆运动学求解失败');
    end
end

%% 备用逆运动学方法
function q_solution = fallback_inverse_kinematics(target_pos, robot)
    % 简化的逆运动学求解，用于逆解失败时的备用方案

    % 基于几何关系的近似解
    q_solution = zeros(4, 1);

    % q1: 基于目标Y坐标的简单映射
    q_solution(1) = atan2(target_pos(2), target_pos(1)) * 0.5;

    % q2: 基于目标X坐标
    q_solution(2) = max(0, min(0.46, target_pos(1) - robot.L_blue - 0.2));

    % q3: 基于目标Y坐标
    green_slide_limit = (robot.W_yellow - robot.W_green) / 2;
    q_solution(3) = max(-green_slide_limit, min(green_slide_limit, target_pos(2) * 0.5));

    % q4: 保持平行于X轴（0度）
    q_solution(4) = 0;

    % 应用关节限制
    q_solution = apply_joint_limits(q_solution, robot);
end

%% 位置和方向误差计算
function error = calc_position_and_orientation_error(q, target_pos, robot)
    % 计算位置误差和方向约束误差的加权和

    [~, T_end] = forward_kinematics_dh(q, robot);
    current_pos = T_end(1:3, 4);

    % 位置误差
    position_error = norm(target_pos - current_pos);

    % 方向约束误差：紫色机械臂应该平行于X轴
    % 这意味着q4应该接近0（或其他使机械臂平行于X轴的角度）
    orientation_error = abs(q(4));  % 希望q4接近0

    % 加权总误差
    error = position_error + 0.1 * orientation_error;
end

%% D-H正运动学函数
function [T_matrices, T_end] = forward_kinematics_dh(q, robot)
    % D-H正运动学计算

    dh_params = robot.dh_params;
    joint_types = robot.joint_types;
    n_joints = length(q);

    T_matrices = cell(n_joints, 1);
    T_cumulative = eye(4);

    for i = 1:n_joints
        % 获取D-H参数
        theta_0 = dh_params(i, 1);
        d_0 = dh_params(i, 2);
        a = dh_params(i, 3);
        alpha = dh_params(i, 4);

        % 根据关节类型确定变量
        if joint_types(i) == 1  % 旋转关节
            theta = theta_0 + q(i);  % theta是变量
            d = d_0;                 % d是常数
        else  % 平移关节
            theta = theta_0;         % theta是常数
            d = d_0 + q(i);         % d是变量
        end

        % 计算D-H变换矩阵
        T_i = dh_transform(theta, d, a, alpha);

        % 累积变换
        T_cumulative = T_cumulative * T_i;
        T_matrices{i} = T_cumulative;
    end

    T_end = T_cumulative;
end

%% 机器人绘制函数（带整体偏移）
function draw_robot_dh_with_offset(q, robot, h_blue_arm, h_yellow_slider, h_green_arm, h_purple_effector, unit_cube_vertices, offset_z)
    % 绘制机器人各部件

    % 整体偏移变换
    T_offset = transl(0, 0, offset_z);

    % 蓝色臂：绕X轴旋转
    T_blue = T_offset * rotx(q(1));
    blue_vertices = transform_part(transl(robot.L_blue/2, 0, 0), ...
                                  diag([robot.L_blue, robot.W_blue, robot.W_blue]), ...
                                  T_blue, unit_cube_vertices);
    set(h_blue_arm, 'Vertices', blue_vertices);

    % 黄色滑块：在蓝色臂上滑动
    T_yellow_base = T_blue;
    T_yellow = T_yellow_base * transl(q(2), 0, robot.W_blue/2 + robot.H_yellow/2);
    yellow_vertices = transform_part(transl(0, 0, 0), ...
                                    diag([robot.L_yellow, robot.W_yellow, robot.H_yellow]), ...
                                    T_yellow, unit_cube_vertices);
    set(h_yellow_slider, 'Vertices', yellow_vertices);

    % 绿色臂：在黄色滑块上方，沿Y轴平移
    T_green_base = T_yellow * transl(0, 0, robot.H_yellow/2 + robot.W_green/2);
    T_green = T_green_base * transl(0, q(3), 0);

    green_vertices = transform_part(transl(0, robot.L_green/2, 0), ...
                                   diag([robot.W_green, robot.L_green, robot.W_green]), ...
                                   T_green, unit_cube_vertices);
    set(h_green_arm, 'Vertices', green_vertices);

    % 紫色机械臂：安装在绿色臂末端，沿X轴方向延伸
    T_green_end = T_green * transl(0, robot.L_green, 0);
    % 绕X轴旋转（关键：这里控制紫色机械臂的方向）
    T_purple = T_green_end * rotx(q(4));

    % 紫色机械臂沿X轴方向延伸
    purple_vertices = transform_part(transl(robot.L_purple/2, 0, 0), ...
                                    diag([robot.L_purple, robot.W_purple, robot.H_purple]), ...
                                    T_purple, unit_cube_vertices);
    set(h_purple_effector, 'Vertices', purple_vertices);
end

%% 辅助函数
function q_limited = apply_joint_limits(q, robot)
    % 应用关节限制
    q_limited = q;
    for i = 1:length(q)
        q_limited(i) = max(robot.q_limits(i,1), min(robot.q_limits(i,2), q(i)));
    end
end

function T = dh_transform(theta, d, a, alpha)
    % 标准D-H变换矩阵
    ct = cos(theta); st = sin(theta);
    ca = cos(alpha); sa = sin(alpha);

    T = [ct,    -st*ca,   st*sa,    a*ct;
         st,     ct*ca,  -ct*sa,    a*st;
         0,      sa,      ca,       d;
         0,      0,       0,        1];
end

function T = rotx(theta)
    % 绕X轴旋转变换矩阵
    c = cos(theta); s = sin(theta);
    T = [1, 0,  0, 0; 0, c, -s, 0; 0, s,  c, 0; 0, 0,  0, 1];
end

function T = transl(x, y, z)
    % 平移变换矩阵
    T = eye(4);
    T(1:3, 4) = [x; y; z];
end

function V_world = transform_part(T_local_pos, S_local, T_world, V_unit)
    % 变换部件顶点
    V_model_4d = T_local_pos * [S_local*V_unit; ones(1, size(V_unit, 2))];
    V_world_4d = T_world * V_model_4d;
    V_world = V_world_4d(1:3, :)';
end
