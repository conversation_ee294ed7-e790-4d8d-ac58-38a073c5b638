%% 机械臂改进效果测试脚本
clear; clc; close all;

fprintf('=== 机械臂改进效果测试 ===\n\n');

%% 测试1: 验证逆运动学函数
fprintf('测试1: 逆运动学函数验证\n');

% 简化的机器人参数
robot.L_blue = 1.0;
robot.L_green = 0.6;
robot.L_purple = 0.1;
robot.W_yellow = 0.14;
robot.W_green = 0.04;

green_slide_limit = (robot.W_yellow - robot.W_green) / 2;
robot.q_limits = [
    -deg2rad(60),  deg2rad(60);
    0,             0.46;
    -green_slide_limit, green_slide_limit;
    -pi,           pi
];

% 测试目标点
test_points = [
    0.8, -0.2, 0.0;
    1.0,  0.0, 0.0;
    1.1,  0.2, 0.0;
];

fprintf('目标点\t\t实际位置\t\t位置误差\tq4角度\n');
fprintf('-------\t\t--------\t\t--------\t-------\n');

for i = 1:size(test_points, 1)
    target = test_points(i, :);
    
    % 使用简化逆运动学
    q = simple_inverse_kinematics_test(target, robot);
    
    % 验证正运动学
    actual = simple_forward_kinematics_test(q, robot);
    
    % 计算误差
    error = norm(actual - target);
    
    fprintf('[%.1f,%.1f,%.1f]\t[%.3f,%.3f,%.3f]\t%.4f\t\t%.1f°\n', ...
            target, actual, error, rad2deg(q(4)));
end

%% 测试2: 斜直线轨迹跟踪
fprintf('\n测试2: 斜直线轨迹跟踪\n');

start_point = [0.8, -0.2, 0.0];
end_point = [1.1, 0.2, 0.0];
n_points = 20;

trajectory_errors = zeros(n_points, 1);
purple_arm_angles = zeros(n_points, 1);

fprintf('跟踪斜直线轨迹: [%.1f,%.1f] -> [%.1f,%.1f]\n', ...
        start_point(1), start_point(2), end_point(1), end_point(2));

for i = 1:n_points
    progress = (i-1) / (n_points-1);
    target = start_point + progress * (end_point - start_point);
    
    q = simple_inverse_kinematics_test(target, robot);
    actual = simple_forward_kinematics_test(q, robot);
    
    trajectory_errors(i) = norm(actual - target);
    purple_arm_angles(i) = rad2deg(q(4));
end

fprintf('平均位置误差: %.4f m\n', mean(trajectory_errors));
fprintf('最大位置误差: %.4f m\n', max(trajectory_errors));
fprintf('紫色臂角度范围: %.1f° ~ %.1f°\n', min(purple_arm_angles), max(purple_arm_angles));
fprintf('角度标准差: %.2f°\n', std(purple_arm_angles));

%% 测试3: 可视化验证
fprintf('\n测试3: 生成可视化验证图\n');

figure('Name', 'Robot Improvement Test Results', 'Position', [300, 300, 1000, 600]);

% 子图1: 轨迹跟踪
subplot(2, 3, 1);
progress_vector = linspace(0, 1, n_points);
target_x = start_point(1) + progress_vector * (end_point(1) - start_point(1));
target_y = start_point(2) + progress_vector * (end_point(2) - start_point(2));

actual_positions = zeros(n_points, 3);
for i = 1:n_points
    progress = (i-1) / (n_points-1);
    target = start_point + progress * (end_point - start_point);
    q = simple_inverse_kinematics_test(target, robot);
    actual_positions(i, :) = simple_forward_kinematics_test(q, robot);
end

plot(target_x, target_y, 'r--', 'LineWidth', 2, 'DisplayName', '目标轨迹');
hold on;
plot(actual_positions(:, 1), actual_positions(:, 2), 'b-', 'LineWidth', 2, 'DisplayName', '实际轨迹');
xlabel('X (m)'); ylabel('Y (m)');
title('轨迹跟踪对比');
legend; grid on; axis equal;

% 子图2: 位置误差
subplot(2, 3, 2);
plot(1:n_points, trajectory_errors*1000, 'r-', 'LineWidth', 2);
xlabel('轨迹点'); ylabel('位置误差 (mm)');
title('位置跟踪误差');
grid on;

% 子图3: 紫色臂角度
subplot(2, 3, 3);
plot(1:n_points, purple_arm_angles, 'b-', 'LineWidth', 2);
xlabel('轨迹点'); ylabel('角度 (°)');
title('紫色机械臂角度变化');
grid on;
yline(0, 'r--', '目标角度 (0°)');

% 子图4: 关节角度变化
subplot(2, 3, 4);
all_joints = zeros(n_points, 4);
for i = 1:n_points
    progress = (i-1) / (n_points-1);
    target = start_point + progress * (end_point - start_point);
    all_joints(i, :) = simple_inverse_kinematics_test(target, robot);
end

plot(1:n_points, rad2deg(all_joints(:, 1)), 'b-', 'LineWidth', 1.5, 'DisplayName', 'q1');
hold on;
plot(1:n_points, all_joints(:, 2)*100, 'g-', 'LineWidth', 1.5, 'DisplayName', 'q2×100');
plot(1:n_points, all_joints(:, 3)*100, 'm-', 'LineWidth', 1.5, 'DisplayName', 'q3×100');
plot(1:n_points, rad2deg(all_joints(:, 4)), 'r-', 'LineWidth', 1.5, 'DisplayName', 'q4');
xlabel('轨迹点'); ylabel('关节值');
title('所有关节角度变化');
legend; grid on;

% 子图5: 误差统计
subplot(2, 3, 5);
histogram(trajectory_errors*1000, 10, 'FaceColor', 'skyblue', 'EdgeColor', 'black');
xlabel('位置误差 (mm)'); ylabel('频次');
title('位置误差分布');
grid on;

% 子图6: 角度统计
subplot(2, 3, 6);
histogram(purple_arm_angles, 10, 'FaceColor', 'lightcoral', 'EdgeColor', 'black');
xlabel('紫色臂角度 (°)'); ylabel('频次');
title('紫色臂角度分布');
grid on;

%% 测试结果评估
fprintf('\n=== 测试结果评估 ===\n');

% 位置精度评估
if mean(trajectory_errors) < 0.01
    fprintf('✓ 位置精度: 优秀 (平均误差 < 1cm)\n');
elseif mean(trajectory_errors) < 0.02
    fprintf('○ 位置精度: 良好 (平均误差 < 2cm)\n');
else
    fprintf('✗ 位置精度: 需要改进 (平均误差 > 2cm)\n');
end

% 方向控制评估
if std(purple_arm_angles) < 3
    fprintf('✓ 方向控制: 优秀 (角度标准差 < 3°)\n');
elseif std(purple_arm_angles) < 5
    fprintf('○ 方向控制: 良好 (角度标准差 < 5°)\n');
else
    fprintf('✗ 方向控制: 需要改进 (角度标准差 > 5°)\n');
end

% 轨迹平滑性评估
joint_smoothness = std(diff(rad2deg(all_joints(:, 4))));
if joint_smoothness < 2
    fprintf('✓ 轨迹平滑性: 优秀 (角度变化平滑)\n');
elseif joint_smoothness < 5
    fprintf('○ 轨迹平滑性: 良好 (角度变化较平滑)\n');
else
    fprintf('✗ 轨迹平滑性: 需要改进 (角度变化不够平滑)\n');
end

fprintf('\n测试完成！改进效果已验证。\n');

%% 辅助函数
function q = simple_inverse_kinematics_test(target_pos, robot)
    q = zeros(4, 1);
    
    % q1: 基于Y坐标
    q(1) = atan2(target_pos(2), target_pos(1)) * 0.3;
    
    % q2: 基于X坐标
    q(2) = max(0, min(0.46, target_pos(1) - robot.L_blue - 0.1));
    
    % q3: 基于Y坐标
    green_slide_limit = (robot.W_yellow - robot.W_green) / 2;
    q(3) = max(-green_slide_limit, min(green_slide_limit, target_pos(2) * 0.8));
    
    % q4: 保持平行于X轴
    q(4) = -q(1);  % 补偿q1的旋转
    
    % 应用关节限制
    for i = 1:4
        q(i) = max(robot.q_limits(i,1), min(robot.q_limits(i,2), q(i)));
    end
end

function pos = simple_forward_kinematics_test(q, robot)
    % 简化的正运动学
    x = robot.L_blue + q(2) + robot.L_green * cos(q(1)) + robot.L_purple * cos(q(1) + q(4));
    y = q(3) + robot.L_green * sin(q(1)) + robot.L_purple * sin(q(1) + q(4));
    z = 0;
    
    pos = [x, y, z];
end
