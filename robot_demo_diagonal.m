%% 机械臂斜直线运动演示 - 紫色机械臂保持平行于X轴
clear; clc; close all;

%% 机器人参数（简化版）
robot.L_blue = 1.0;         % 蓝色臂长度
robot.W_blue = 0.06;        % 蓝色臂宽度
robot.L_yellow = 0.15;      % 黄色滑块长度
robot.W_yellow = 0.14;      % 黄色滑块宽度
robot.H_yellow = 0.05;      % 黄色滑块高度
robot.L_green = 0.6;        % 绿色臂长度
robot.W_green = 0.04;       % 绿色臂宽度
robot.L_purple = 0.1;       % 紫色执行器长度
robot.W_purple = 0.07;      % 紫色执行器宽度
robot.H_purple = 0.07;      % 紫色执行器高度

% 关节限制
green_slide_limit = (robot.W_yellow - robot.W_green) / 2;
robot.q_limits = [
    -deg2rad(60),  deg2rad(60);     % q1: 蓝色臂绕X轴旋转 ±60°
    0,             0.46;            % q2: 黄色滑块沿X轴平移 0-0.46m
    -green_slide_limit, green_slide_limit;  % q3: 绿色臂Y轴平移
    -pi,           pi               % q4: 紫色执行器绕X轴旋转 ±180°
];

%% 斜直线轨迹定义
trajectory.start_point = [0.8, -0.2, -0.05];   % 起始点
trajectory.end_point = [1.1, 0.2, 0.05];       % 终止点
trajectory.duration = 6.0;                      % 持续时间

fprintf('=== 机械臂斜直线运动演示 ===\n');
fprintf('起始点: [%.2f, %.2f, %.2f]\n', trajectory.start_point);
fprintf('终止点: [%.2f, %.2f, %.2f]\n', trajectory.end_point);
fprintf('轨迹长度: %.3f m\n', norm(trajectory.end_point - trajectory.start_point));
fprintf('控制目标: 紫色机械臂保持平行于X轴\n\n');

%% 图形设置
figure('Name', 'Robot Diagonal Motion Demo', 'NumberTitle', 'off', ...
       'Color', 'w', 'Position', [200, 200, 1200, 800]);

% 创建子图
subplot(2, 2, [1, 3]);  % 主要的3D视图
hold on; axis equal; grid on;
view(135, 25);
xlabel('X (m)'); ylabel('Y (m)'); zlabel('Z (m)');
title('机械臂斜直线运动 - 紫色臂保持平行X轴');
xlim([0, 1.3]); ylim([-0.5, 0.5]); zlim([-0.3, 0.2]);

% 绘制目标轨迹
plot3([trajectory.start_point(1), trajectory.end_point(1)], ...
      [trajectory.start_point(2), trajectory.end_point(2)], ...
      [trajectory.start_point(3), trajectory.end_point(3)], ...
      'r--', 'LineWidth', 3, 'DisplayName', '目标轨迹');

% 创建机器人部件（简化显示）
h_end_effector = plot3(0, 0, 0, 'mo', 'MarkerSize', 10, 'MarkerFaceColor', 'm', 'DisplayName', '末端执行器');
h_actual_path = plot3([], [], [], 'b-', 'LineWidth', 2, 'DisplayName', '实际路径');
legend('Location', 'best');

% 子图2：关节角度变化
subplot(2, 2, 2);
h_joint_plot = plot(0, [0, 0, 0, 0], 'o-', 'LineWidth', 2);
xlabel('时间 (s)'); ylabel('关节角度');
title('关节角度变化');
grid on;
legend({'q1 (°)', 'q2 (m)', 'q3 (m)', 'q4 (°)'}, 'Location', 'best');

% 子图3：误差分析
subplot(2, 2, 4);
h_error_plot = plot(0, 0, 'r-', 'LineWidth', 2);
xlabel('时间 (s)'); ylabel('位置误差 (m)');
title('轨迹跟踪误差');
grid on;

%% 仿真主循环
fprintf('开始仿真...\n');

dt = 0.1;  % 时间步长
time_vector = 0:dt:trajectory.duration;
n_steps = length(time_vector);

% 存储数据
actual_positions = zeros(n_steps, 3);
joint_angles = zeros(n_steps, 4);
position_errors = zeros(n_steps, 1);
purple_arm_angles = zeros(n_steps, 1);

for i = 1:n_steps
    t = time_vector(i);
    
    % 计算当前目标位置
    progress = t / trajectory.duration;
    target_pos = trajectory.start_point + progress * (trajectory.end_point - trajectory.start_point);
    
    % 逆运动学求解（简化版）
    q_solution = simple_inverse_kinematics(target_pos, robot);
    
    % 正运动学验证
    actual_pos = simple_forward_kinematics(q_solution, robot);
    
    % 存储数据
    actual_positions(i, :) = actual_pos;
    joint_angles(i, :) = q_solution;
    position_errors(i) = norm(actual_pos - target_pos);
    purple_arm_angles(i) = rad2deg(q_solution(4));
    
    % 更新显示
    if mod(i, 5) == 1  % 每5步更新一次显示
        % 更新3D视图
        subplot(2, 2, [1, 3]);
        set(h_end_effector, 'XData', actual_pos(1), 'YData', actual_pos(2), 'ZData', actual_pos(3));
        set(h_actual_path, 'XData', actual_positions(1:i, 1), ...
                          'YData', actual_positions(1:i, 2), ...
                          'ZData', actual_positions(1:i, 3));
        
        % 更新关节角度图
        subplot(2, 2, 2);
        plot(time_vector(1:i), [rad2deg(joint_angles(1:i, 1)), ...
                               joint_angles(1:i, 2)*100, ...  % 放大显示
                               joint_angles(1:i, 3)*100, ...  % 放大显示
                               rad2deg(joint_angles(1:i, 4))]);
        xlabel('时间 (s)'); ylabel('关节值');
        title('关节变化 (角度°, 位移×100)');
        grid on; legend({'q1 (°)', 'q2 (×100)', 'q3 (×100)', 'q4 (°)'}, 'Location', 'best');
        
        % 更新误差图
        subplot(2, 2, 4);
        plot(time_vector(1:i), position_errors(1:i), 'r-', 'LineWidth', 2);
        xlabel('时间 (s)'); ylabel('位置误差 (m)');
        title('轨迹跟踪误差');
        grid on;
        
        drawnow;
    end
end

%% 结果分析
fprintf('\n=== 仿真结果分析 ===\n');
fprintf('平均位置误差: %.4f m\n', mean(position_errors));
fprintf('最大位置误差: %.4f m\n', max(position_errors));
fprintf('紫色机械臂角度范围: %.1f° ~ %.1f°\n', min(purple_arm_angles), max(purple_arm_angles));
fprintf('紫色机械臂角度标准差: %.2f°\n', std(purple_arm_angles));

if std(purple_arm_angles) < 5
    fprintf('✓ 紫色机械臂成功保持接近平行于X轴！\n');
else
    fprintf('✗ 紫色机械臂方向控制需要改进。\n');
end

%% 简化的逆运动学函数
function q = simple_inverse_kinematics(target_pos, robot)
    % 简化的逆运动学求解，专注于保持紫色机械臂平行于X轴
    
    q = zeros(4, 1);
    
    % q1: 基于目标位置的Y坐标
    q(1) = atan2(target_pos(2), target_pos(1)) * 0.3;  % 减小系数以保持稳定
    
    % q2: 基于目标位置的X坐标
    q(2) = max(0, min(0.46, target_pos(1) - robot.L_blue - 0.1));
    
    % q3: 基于目标位置的Y坐标，但限制范围
    green_slide_limit = (robot.W_yellow - robot.W_green) / 2;
    q(3) = max(-green_slide_limit, min(green_slide_limit, target_pos(2) * 0.8));
    
    % q4: 关键 - 保持紫色机械臂平行于X轴
    % 通过补偿前面关节的旋转来保持水平
    q(4) = -q(1);  % 反向补偿q1的旋转
    
    % 应用关节限制
    for i = 1:4
        q(i) = max(robot.q_limits(i,1), min(robot.q_limits(i,2), q(i)));
    end
end

%% 简化的正运动学函数
function pos = simple_forward_kinematics(q, robot)
    % 简化的正运动学计算末端位置
    
    % 基于几何关系的近似计算
    x = robot.L_blue + q(2) + robot.L_green * cos(q(1)) + robot.L_purple * cos(q(1) + q(4));
    y = q(3) + robot.L_green * sin(q(1)) + robot.L_purple * sin(q(1) + q(4));
    z = 0;  % 简化为2D问题
    
    pos = [x, y, z];
end

fprintf('\n演示完成！\n');
